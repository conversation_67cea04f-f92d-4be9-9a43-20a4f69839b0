-- Merging decision tree log ---
application
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e08585f7cb9425466bb2e9923464bc3d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e08585f7cb9425466bb2e9923464bc3d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml
manifest
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1cd1f182cdbf7c211dfcc1b57894129\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95323550d17b3d14bf80f8bd20fcde82\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b51bd7ae538d13edf5ff2bba0e0c22d9\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bad5164634845e227f773a262d6035e\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6bd24a9aa3d936e2181b0f91106d28e\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f774590ff8fb50ae93b92d06a0ae7753\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fad1dca6cfe10b7840f660c3396beba\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8d96466c79ef3c4c478cddd58118971\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9c0eea51773d21dd7d2eb04f96ca22c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4f234002de211db34ce7dc9438e5dbf\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\418dca6da2e008ca3f49bc824f5f60db\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d950a7e594cc2aced998a8ea224758f0\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\71637354b4a47fd70a9fdd2aa33ba92c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\de47ebcdbed56d6ce4dbe70abe07e60f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4d96654ae3ca4fea304b29fa162bd4d\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f4039c93e916e99db95dea0ed9da2e6a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\154a92cc1ef59e7b109bc2e3155cd14c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e08585f7cb9425466bb2e9923464bc3d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83eb6394ac6dda52f43437dde0af1a44\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f86f7cd201e5ab6495f8c2b243c021f5\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ee193b1b1bf67ad8a03bc0769301bae\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1cd1f182cdbf7c211dfcc1b57894129\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1cd1f182cdbf7c211dfcc1b57894129\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95323550d17b3d14bf80f8bd20fcde82\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\95323550d17b3d14bf80f8bd20fcde82\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b51bd7ae538d13edf5ff2bba0e0c22d9\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b51bd7ae538d13edf5ff2bba0e0c22d9\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bad5164634845e227f773a262d6035e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bad5164634845e227f773a262d6035e\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6bd24a9aa3d936e2181b0f91106d28e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f6bd24a9aa3d936e2181b0f91106d28e\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f774590ff8fb50ae93b92d06a0ae7753\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f774590ff8fb50ae93b92d06a0ae7753\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fad1dca6cfe10b7840f660c3396beba\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fad1dca6cfe10b7840f660c3396beba\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8d96466c79ef3c4c478cddd58118971\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b8d96466c79ef3c4c478cddd58118971\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9c0eea51773d21dd7d2eb04f96ca22c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a9c0eea51773d21dd7d2eb04f96ca22c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4f234002de211db34ce7dc9438e5dbf\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4f234002de211db34ce7dc9438e5dbf\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\418dca6da2e008ca3f49bc824f5f60db\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\418dca6da2e008ca3f49bc824f5f60db\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d950a7e594cc2aced998a8ea224758f0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d950a7e594cc2aced998a8ea224758f0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\71637354b4a47fd70a9fdd2aa33ba92c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\71637354b4a47fd70a9fdd2aa33ba92c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\de47ebcdbed56d6ce4dbe70abe07e60f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\de47ebcdbed56d6ce4dbe70abe07e60f\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4d96654ae3ca4fea304b29fa162bd4d\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4d96654ae3ca4fea304b29fa162bd4d\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f4039c93e916e99db95dea0ed9da2e6a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f4039c93e916e99db95dea0ed9da2e6a\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\154a92cc1ef59e7b109bc2e3155cd14c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\154a92cc1ef59e7b109bc2e3155cd14c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e08585f7cb9425466bb2e9923464bc3d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e08585f7cb9425466bb2e9923464bc3d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83eb6394ac6dda52f43437dde0af1a44\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\83eb6394ac6dda52f43437dde0af1a44\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f86f7cd201e5ab6495f8c2b243c021f5\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f86f7cd201e5ab6495f8c2b243c021f5\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ee193b1b1bf67ad8a03bc0769301bae\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\3ee193b1b1bf67ad8a03bc0769301bae\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\FLUTTER_APPS\Dino_Run\my_app\android\app\src\debug\AndroidManifest.xml
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dde0ce4641a9e64f1a82e2c17dee675f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.my_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.my_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2860303c6438e0c5df29ef12c60e1965\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\aa8e7fefd6b896e74d433ba335e41027\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\89669b86791a7edcbcebca34eedf8a0d\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5f4bd1ebf5c467196d50d86558045295\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
