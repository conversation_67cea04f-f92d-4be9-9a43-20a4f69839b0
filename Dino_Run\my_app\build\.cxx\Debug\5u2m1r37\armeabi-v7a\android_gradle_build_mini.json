{"buildFiles": ["E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\5u2m1r37\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\5u2m1r37\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}