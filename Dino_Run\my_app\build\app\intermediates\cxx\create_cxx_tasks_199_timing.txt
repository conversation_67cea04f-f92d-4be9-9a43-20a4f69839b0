# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 17ms
    create-ARMEABI_V7A-model 20ms
    create-ARM64_V8A-model 21ms
    create-X86-model 11ms
    create-module-model
      create-ndk-meta-abi-list 24ms
    create-module-model completed in 36ms
    create-variant-model 31ms
    create-ARMEABI_V7A-model 26ms
    [gap of 13ms]
    create-ARM64_V8A-model 20ms
    create-X86-model 26ms
    create-X86_64-model 43ms
    [gap of 18ms]
    create-ARMEABI_V7A-model 11ms
    create-ARM64_V8A-model 51ms
    create-X86-model 15ms
  create-initial-cxx-model completed in 390ms
create_cxx_tasks completed in 397ms

