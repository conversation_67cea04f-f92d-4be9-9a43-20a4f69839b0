# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 67ms
    create-variant-model 43ms
    create-ARMEABI_V7A-model 255ms
    create-ARM64_V8A-model 39ms
    create-X86-model 10ms
    create-X86_64-model 21ms
    [gap of 16ms]
    create-ARMEABI_V7A-model 24ms
    create-ARM64_V8A-model 109ms
    create-X86-model 12ms
    create-module-model 12ms
    [gap of 24ms]
    create-X86-model 16ms
    create-X86_64-model 12ms
  create-initial-cxx-model completed in 686ms
  [gap of 52ms]
create_cxx_tasks completed in 780ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 12ms]
    create-ARMEABI_V7A-model 11ms
    [gap of 32ms]
    create-variant-model 11ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 12ms
    create-X86_64-model 16ms
    create-module-model 11ms
    create-variant-model 33ms
    create-ARMEABI_V7A-model 47ms
    create-ARM64_V8A-model 20ms
    create-X86-model 18ms
    create-X86_64-model 47ms
  create-initial-cxx-model completed in 310ms
  [gap of 21ms]
create_cxx_tasks completed in 332ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 17ms
    create-variant-model 11ms
    create-ARM64_V8A-model 15ms
    create-X86-model 26ms
    create-X86_64-model 17ms
    [gap of 31ms]
    create-module-model 43ms
    create-variant-model 41ms
    create-ARMEABI_V7A-model 86ms
    [gap of 24ms]
    create-ARM64_V8A-model 32ms
    [gap of 25ms]
    create-ARMEABI_V7A-model 16ms
    create-ARM64_V8A-model 27ms
    create-X86-model 71ms
  create-initial-cxx-model completed in 514ms
  [gap of 84ms]
create_cxx_tasks completed in 598ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 145ms
    [gap of 18ms]
    create-variant-model 47ms
    create-ARMEABI_V7A-model 163ms
    create-ARM64_V8A-model 115ms
    create-X86-model 83ms
    create-X86_64-model 20ms
    create-module-model 13ms
    create-variant-model 30ms
    create-ARMEABI_V7A-model 51ms
    create-ARM64_V8A-model 65ms
    [gap of 12ms]
    create-X86-model 78ms
    create-X86_64-model 41ms
    create-module-model 18ms
    create-variant-model 24ms
    create-ARMEABI_V7A-model 17ms
    create-ARM64_V8A-model 19ms
    create-X86-model 19ms
    create-X86_64-model 27ms
  create-initial-cxx-model completed in 1050ms
  [gap of 184ms]
create_cxx_tasks completed in 1268ms

