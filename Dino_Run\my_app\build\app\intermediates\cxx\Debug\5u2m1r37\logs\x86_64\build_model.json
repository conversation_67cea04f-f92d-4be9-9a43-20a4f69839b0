{"abi": "X86_64", "info": {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\5u2m1r37\\x86_64", "soFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cxx\\Debug\\5u2m1r37\\obj\\x86_64", "soRepublishFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cmake\\profile\\obj\\x86_64", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-Wno-dev", "--no-warn-unused-cli"], "cFlagsList": [], "cppFlagsList": [], "variantName": "profile", "isDebuggableEnabled": true, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx", "intermediatesBaseFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates", "intermediatesFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\android\\app", "moduleBuildFile": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\android\\app\\build.gradle", "makeFile": "E:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "ndkVersion": "26.3.11579264", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\5u2m1r37\\prefab\\x86_64", "isActiveAbi": true, "fullConfigurationHash": "5u2m1r3703o5qz5l6n4w5r412s3d6j6kv1g14r505n2e1y1y42ks4f5p12", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.1.0.\n#   - $NDK is the path to NDK 26.3.11579264.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HE:/Flutter/flutter/packages/flutter_tools/gradle/src/main/scripts\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-<PERSON><PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:/FLUTTER_APPS/Dino_Run/my_app/build/app/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:/FLUTTER_APPS/Dino_Run/my_app/build/app/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-BE:/FLUTTER_APPS/Dino_Run/my_app/build/.cxx/Debug/$HASH/$ABI\n-GNinja\n-Wno-dev\n--no-warn-unused-cli", "configurationArguments": ["-HE:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=x86_64", "-DCMAKE_ANDROID_ARCH_ABI=x86_64", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cxx\\Debug\\5u2m1r37\\obj\\x86_64", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cxx\\Debug\\5u2m1r37\\obj\\x86_64", "-DCMAKE_BUILD_TYPE=Debug", "-BE:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\.cxx\\Debug\\5u2m1r37\\x86_64", "-<PERSON><PERSON><PERSON><PERSON>", "-Wno-dev", "--no-warn-unused-cli"], "intermediatesParentFolder": "E:\\FLUTTER_APPS\\Dino_Run\\my_app\\build\\app\\intermediates\\cxx\\Debug\\5u2m1r37"}